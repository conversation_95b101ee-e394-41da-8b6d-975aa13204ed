@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  html {
    font-family: "Inter", system-ui, sans-serif;
  }

  body {
    @apply bg-white text-gray-900 antialiased;
  }
}

/* Custom component styles */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800;
  }

  .btn-secondary {
    @apply bg-white text-primary-600 border-primary-600 hover:bg-primary-50 focus:ring-primary-500;
  }

  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }

  .btn-warning {
    @apply bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-400;
  }

  .btn-info {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  .btn-light {
    @apply bg-gray-100 text-gray-900 border-gray-300 hover:bg-gray-200 focus:ring-gray-500;
  }

  .btn-dark {
    @apply bg-gray-800 text-white hover:bg-gray-900 focus:ring-gray-700;
  }

  .btn-small {
    @apply px-4 py-2 text-sm;
  }

  .btn-large {
    @apply px-8 py-4 text-lg;
  }

  .card {
    @apply bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden transition-all duration-300;
  }

  .card-hover:hover {
    @apply shadow-xl transform -translate-y-1;
  }

  .form-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }
}

/* Custom utility styles */
@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent;
  }

  .bg-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-blue-600;
  }

  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
}
