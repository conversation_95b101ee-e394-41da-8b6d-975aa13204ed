import React from 'react';

const Alert = ({ 
  children, 
  variant = 'info', 
  dismissible = false, 
  onDismiss,
  className = '',
  ...props 
}) => {
  const baseClasses = 'p-4 rounded-lg border flex items-start space-x-3';
  
  const variantClasses = {
    success: 'bg-green-50 border-green-200 text-green-800',
    error: 'bg-red-50 border-red-200 text-red-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    info: 'bg-blue-50 border-blue-200 text-blue-800',
  };

  const iconClasses = {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️',
  };

  const alertClass = `
    ${baseClasses}
    ${variantClasses[variant] || variantClasses.info}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  return (
    <div className={alertClass} {...props}>
      <span className="text-lg flex-shrink-0">
        {iconClasses[variant]}
      </span>
      <div className="flex-1">
        {children}
      </div>
      {dismissible && (
        <button
          onClick={onDismiss}
          className="flex-shrink-0 ml-4 text-current hover:opacity-70 transition-opacity duration-200"
        >
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      )}
    </div>
  );
};

export default Alert;
