import Button from "../components/Button";

const Home = () => {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-600 via-blue-600 to-purple-700 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
          <div className="text-center space-y-8 animate-fade-in">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight">
              Selamat Datang di{" "}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-400">
                Frontend App
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
              Kerangka frontend modern yang dibangun dengan <PERSON>, <PERSON><PERSON>, dan
              <PERSON> CSS untuk pengembangan aplikasi web yang cepat dan
              efisien
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8">
              <Button
                variant="primary"
                className="bg-white text-primary-600 hover:bg-gray-100 shadow-lg transform hover:scale-105 transition-all duration-200"
              >
                🚀 Get Started
              </Button>
              <Button
                variant="secondary"
                className="border-white text-white hover:bg-white hover:text-primary-600 shadow-lg transform hover:scale-105 transition-all duration-200"
              >
                📖 Learn More
              </Button>
            </div>
          </div>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-10 rounded-full"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white opacity-10 rounded-full"></div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Fitur Utama
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Teknologi modern dan tools terbaik untuk pengembangan aplikasi web
              yang efisien
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
              <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                ⚡
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">
                Fast Development
              </h3>
              <p className="text-gray-600 leading-relaxed">
                Menggunakan Vite untuk development yang super cepat dengan hot
                reload dan build yang optimal
              </p>
            </div>

            <div className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
              <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                🎨
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">
                Modern UI
              </h3>
              <p className="text-gray-600 leading-relaxed">
                Komponen React yang dapat digunakan kembali dengan Tailwind CSS
                untuk styling yang modern
              </p>
            </div>

            <div className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
              <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                📱
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">
                Responsive Design
              </h3>
              <p className="text-gray-600 leading-relaxed">
                Design yang responsif dan mobile-friendly dengan utility-first
                CSS framework
              </p>
            </div>

            <div className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
              <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                🔧
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">
                Easy Setup
              </h3>
              <p className="text-gray-600 leading-relaxed">
                Setup yang mudah dengan struktur folder yang terorganisir dan
                dokumentasi lengkap
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Tech Stack Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Tech Stack
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Dibangun dengan teknologi terdepan untuk performa dan developer
              experience terbaik
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center group">
              <div className="w-20 h-20 mx-auto mb-4 bg-blue-100 rounded-2xl flex items-center justify-center group-hover:bg-blue-200 transition-colors duration-300">
                <span className="text-3xl">⚛️</span>
              </div>
              <h3 className="font-semibold text-gray-900">React 19</h3>
              <p className="text-gray-600 text-sm">UI Library</p>
            </div>

            <div className="text-center group">
              <div className="w-20 h-20 mx-auto mb-4 bg-purple-100 rounded-2xl flex items-center justify-center group-hover:bg-purple-200 transition-colors duration-300">
                <span className="text-3xl">⚡</span>
              </div>
              <h3 className="font-semibold text-gray-900">Vite</h3>
              <p className="text-gray-600 text-sm">Build Tool</p>
            </div>

            <div className="text-center group">
              <div className="w-20 h-20 mx-auto mb-4 bg-cyan-100 rounded-2xl flex items-center justify-center group-hover:bg-cyan-200 transition-colors duration-300">
                <span className="text-3xl">🎨</span>
              </div>
              <h3 className="font-semibold text-gray-900">Tailwind CSS</h3>
              <p className="text-gray-600 text-sm">Styling</p>
            </div>

            <div className="text-center group">
              <div className="w-20 h-20 mx-auto mb-4 bg-green-100 rounded-2xl flex items-center justify-center group-hover:bg-green-200 transition-colors duration-300">
                <span className="text-3xl">🧪</span>
              </div>
              <h3 className="font-semibold text-gray-900">Vitest</h3>
              <p className="text-gray-600 text-sm">Testing</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
