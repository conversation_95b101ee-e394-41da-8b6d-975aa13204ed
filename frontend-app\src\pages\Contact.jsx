import React, { useState } from 'react';
import Button from '../components/Button';
import '../styles/Contact.css';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    // Di sini Anda bisa menambahkan logika untuk mengirim data ke backend
    alert('Pesan berhasil dikirim!');
    setFormData({ name: '', email: '', message: '' });
  };

  return (
    <div className="contact">
      <div className="container">
        <h1>Hu<PERSON>ngi <PERSON></h1>

        <div className="contact-content">
          <div className="contact-info">
            <h2>Informasi Kontak</h2>
            <div className="contact-item">
              <h3>📧 Email</h3>
              <p><EMAIL></p>
            </div>
            <div className="contact-item">
              <h3>📞 Telepon</h3>
              <p>+62 123 456 789</p>
            </div>
            <div className="contact-item">
              <h3>📍 Alamat</h3>
              <p>Jakarta, Indonesia</p>
            </div>
          </div>

          <div className="contact-form">
            <h2>Kirim Pesan</h2>
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="name">Nama</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="email">Email</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="message">Pesan</label>
                <textarea
                  id="message"
                  name="message"
                  rows="5"
                  value={formData.message}
                  onChange={handleChange}
                  required
                ></textarea>
              </div>
              <button type="submit" className="btn btn-primary">Kirim Pesan</button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contact;
