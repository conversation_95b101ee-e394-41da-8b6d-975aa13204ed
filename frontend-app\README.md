# Frontend App - Kerangka Frontend Modern

Kerangka frontend modern yang dibangun dengan React, Vite, Tailwind CSS, dan <PERSON>.js. Aplikasi ini menyediakan struktur yang terorganisir dan komponen yang dapat digunakan kembali untuk pengembangan aplikasi web yang cepat dan efisien.

## 🚀 Fitur Utama

- **React 19** - Library JavaScript modern untuk membangun user interface
- **Vite** - Build tool yang sangat cepat dengan hot reload
- **Tailwind CSS** - Utility-first CSS framework untuk styling yang cepat
- **React Router** - Routing untuk single page application
- **Responsive Design** - Design yang mobile-friendly
- **Struktur Terorganisir** - Folder dan komponen yang terstruktur dengan baik
- **Testing Ready** - Setup testing dengan Vitest dan Testing Library

## 📁 Struktur Project

```
frontend-app/
├── public/
│   └── vite.svg
├── src/
│   ├── assets/          # Asset statis (gambar, icon, dll)
│   ├── components/      # Komponen React yang dapat digunakan kembali
│   │   ├── Header.jsx
│   │   ├── Footer.jsx
│   │   └── Layout.jsx
│   ├── pages/          # Halaman-halaman aplikasi
│   │   ├── Home.jsx
│   │   ├── About.jsx
│   │   └── Contact.jsx
│   ├── styles/         # File CSS untuk styling
│   │   ├── Layout.css
│   │   ├── Header.css
│   │   ├── Footer.css
│   │   ├── Home.css
│   │   ├── About.css
│   │   └── Contact.css
│   ├── utils/          # Utility functions
│   ├── App.jsx         # Komponen utama aplikasi
│   ├── main.jsx        # Entry point aplikasi
│   └── index.css       # Global CSS
├── package.json
├── vite.config.js
└── README.md
```

## 🛠️ Instalasi dan Setup

### Prerequisites

- Node.js (versi 16 atau lebih baru)
- npm atau yarn

### Langkah Instalasi

1. **Clone atau download project ini**
2. **Install dependencies:**

   ```bash
   npm install
   ```

3. **Jalankan development server:**

   ```bash
   npm run dev
   ```

4. **Buka browser dan akses:**
   ```
   http://localhost:5173
   ```

## 📜 Scripts yang Tersedia

- `npm run dev` - Menjalankan development server
- `npm run build` - Build aplikasi untuk production
- `npm run preview` - Preview build production secara lokal
- `npm run lint` - Menjalankan ESLint untuk code quality

## 🎨 Komponen yang Tersedia

### Layout Components

- **Header** - Navigation bar dengan menu utama
- **Footer** - Footer dengan informasi kontak dan links
- **Layout** - Wrapper component untuk struktur halaman

### Pages

- **Home** - Halaman utama dengan hero section dan features
- **About** - Halaman tentang aplikasi
- **Contact** - Halaman kontak dengan form

## 🔧 Kustomisasi

### Menambah Halaman Baru

1. Buat file JSX baru di folder `src/pages/`
2. Buat file CSS untuk styling di folder `src/styles/`
3. Tambahkan route baru di `App.jsx`
4. Update navigation di `Header.jsx`

### Menambah Komponen Baru

1. Buat file JSX baru di folder `src/components/`
2. Import dan gunakan di halaman yang diperlukan

### Styling

- Global styles: `src/index.css`
- Component-specific styles: `src/styles/[ComponentName].css`
- Menggunakan CSS modern dengan flexbox dan grid

## 🌐 Teknologi yang Digunakan

- **React 19** - UI Library
- **Vite** - Build Tool & Dev Server
- **Tailwind CSS** - Utility-first CSS Framework
- **React Router DOM** - Client-side Routing
- **Vitest** - Testing Framework
- **ESLint** - Code Quality

## 📱 Responsive Design

Aplikasi ini sudah dioptimasi untuk berbagai ukuran layar:

- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (< 768px)

## 🚀 Deployment

Untuk deploy ke production:

1. **Build aplikasi:**

   ```bash
   npm run build
   ```

2. **Upload folder `dist/` ke hosting provider Anda**

### Hosting Recommendations

- **Vercel** - Optimal untuk React apps
- **Netlify** - Static site hosting
- **GitHub Pages** - Gratis untuk project open source

## 🤝 Kontribusi

Silakan berkontribusi untuk meningkatkan kerangka ini:

1. Fork repository
2. Buat feature branch
3. Commit perubahan
4. Push ke branch
5. Buat Pull Request

## 📄 Lisensi

Project ini menggunakan lisensi MIT. Silakan gunakan untuk project pribadi maupun komersial.

## 📞 Support

Jika ada pertanyaan atau butuh bantuan:

- Email: <EMAIL>
- GitHub Issues: [Create Issue](https://github.com/username/frontend-app/issues)
